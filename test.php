<?php
echo "PHP is working!<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test MySQL connection
$host = 'localhost';
$dbname = 'wp';
$username = 'wp';
$password = '#mdsrabon13';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "MySQL connection: SUCCESS<br>";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM wp_posts");
    $result = $stmt->fetch();
    echo "Total posts in database: " . $result['count'] . "<br>";
    
} catch(PDOException $e) {
    echo "MySQL connection: FAILED - " . $e->getMessage() . "<br>";
}

phpinfo();
?>
