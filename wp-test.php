<?php
// Minimal WordPress test
define('WP_USE_THEMES', false);
require_once('wp-load.php');

echo "<h1>WordPress Test</h1>";
echo "<p>WordPress loaded successfully!</p>";
echo "<p>Site URL: " . get_site_url() . "</p>";
echo "<p>Home URL: " . get_home_url() . "</p>";
echo "<p>Active Theme: " . get_template() . "</p>";

// Check database connection
global $wpdb;
$result = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts}");
echo "<p>Total posts in database: " . $result . "</p>";

// Check active plugins
$active_plugins = get_option('active_plugins');
echo "<h2>Active Plugins:</h2>";
if (!empty($active_plugins)) {
    echo "<ul>";
    foreach ($active_plugins as $plugin) {
        echo "<li>" . $plugin . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No active plugins</p>";
}

// Memory usage
echo "<h2>System Info:</h2>";
echo "<p>Memory Usage: " . memory_get_usage(true) / 1024 / 1024 . " MB</p>";
echo "<p>Peak Memory: " . memory_get_peak_usage(true) / 1024 / 1024 . " MB</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . " seconds</p>";
?>
