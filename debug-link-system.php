<?php
/**
 * Debug Link System
 * Test file to check current link system configuration
 */

// Include WordPress
require_once('wp-config.php');

echo "<h1>Link System Debug</h1>";

// Check current settings
echo "<h2>Current Link Settings:</h2>";
echo "<ul>";
echo "<li><strong>Link Time Wait:</strong> " . dooplay_get_option('linktimewait', 'Not Set') . " seconds</li>";
echo "<li><strong>Link Output Type:</strong> " . dooplay_get_option('linkoutputtype', 'Not Set') . "</li>";
echo "<li><strong>Link Button Text:</strong> " . dooplay_get_option('linkbtntext', 'Not Set') . "</li>";
echo "<li><strong>Link Button Text Under:</strong> " . dooplay_get_option('linkbtntextunder', 'Not Set') . "</li>";
echo "<li><strong>Link Button Color:</strong> " . dooplay_get_option('linkbtncolor', 'Not Set') . "</li>";
echo "</ul>";

// Check shorteners
$shorteners = dooplay_get_option('shorteners');
echo "<h2>Shorteners Configuration:</h2>";
if (!empty($shorteners) && is_array($shorteners)) {
    echo "<ul>";
    foreach ($shorteners as $index => $shortener) {
        echo "<li>Shortener " . ($index + 1) . ": " . (isset($shortener['short']) ? $shortener['short'] : 'Not configured') . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p><strong>No shorteners configured</strong></p>";
}

// Test membership function
echo "<h2>Membership System Test:</h2>";
if (function_exists('dooplay_check_premium_membership')) {
    echo "<p>Premium membership function exists</p>";
    
    // Test with current user
    if (is_user_logged_in()) {
        $is_premium = dooplay_check_premium_membership();
        echo "<p>Current user premium status: " . ($is_premium ? 'Premium' : 'Regular') . "</p>";
        
        // Get user details
        $user_id = get_current_user_id();
        echo "<p>User ID: " . $user_id . "</p>";
        
        // Check ARMember details if available
        if (function_exists('arm_get_user_membership_detail')) {
            $user_membership = arm_get_user_membership_detail($user_id);
            echo "<p>ARMember membership details:</p>";
            echo "<pre>" . print_r($user_membership, true) . "</pre>";
        }
    } else {
        echo "<p>No user logged in</p>";
    }
} else {
    echo "<p><strong>Premium membership function not found</strong></p>";
}

// Test a sample link
echo "<h2>Sample Link Test:</h2>";
$sample_url = "https://example.com/sample-file.mp4";
echo "<p>Original URL: " . $sample_url . "</p>";

if (class_exists('DooLinks')) {
    $shortened_url = DooLinks::shorteners($sample_url);
    echo "<p>After shorteners: " . $shortened_url . "</p>";
} else {
    echo "<p><strong>DooLinks class not found</strong></p>";
}

// Check if we have any dt_links posts
echo "<h2>Sample dt_links Posts:</h2>";
$links = get_posts(array(
    'post_type' => 'dt_links',
    'numberposts' => 5,
    'post_status' => 'any'
));

if (!empty($links)) {
    echo "<ul>";
    foreach ($links as $link) {
        $url = get_post_meta($link->ID, '_dool_url', true);
        $type = get_post_meta($link->ID, '_dool_type', true);
        $premium = get_post_meta($link->ID, '_dool_premium', true);
        
        echo "<li>";
        echo "<strong>Link ID:</strong> " . $link->ID . "<br>";
        echo "<strong>Title:</strong> " . $link->post_title . "<br>";
        echo "<strong>URL:</strong> " . $url . "<br>";
        echo "<strong>Type:</strong> " . $type . "<br>";
        echo "<strong>Premium:</strong> " . ($premium == '1' ? 'Yes' : 'No') . "<br>";
        echo "<strong>Permalink:</strong> " . get_permalink($link->ID) . "<br>";
        echo "</li><br>";
    }
    echo "</ul>";
} else {
    echo "<p>No dt_links posts found</p>";
}

?>
