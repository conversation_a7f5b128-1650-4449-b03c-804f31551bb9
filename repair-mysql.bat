@echo off
echo ========================================
echo MySQL Database Repair Script
echo ========================================

REM Stop MySQL if running
echo Stopping MySQL...
taskkill /F /IM mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

REM Backup current data
echo Creating backup...
if not exist "d:\xampp\mysql\data-backup" (
    mkdir "d:\xampp\mysql\data-backup"
)
xcopy "d:\xampp\mysql\data\mysql" "d:\xampp\mysql\data-backup\mysql" /E /I /Y >nul

REM Try to repair the database
echo Repairing MySQL database...
cd /d "d:\xampp\mysql\bin"

REM Start MySQL in safe mode
echo Starting MySQL in safe mode...
start "MySQL Safe" mysqld.exe --defaults-file=my.ini --skip-grant-tables --skip-networking --console

REM Wait for MySQL to start
timeout /t 10 /nobreak >nul

REM Check if MySQL started
netstat -an | findstr ":3306" >nul
if %errorlevel% == 0 (
    echo MySQL started in safe mode
    
    REM Try to repair tables
    echo Repairing tables...
    mysql.exe -u root --skip-password mysql -e "REPAIR TABLE db;"
    mysql.exe -u root --skip-password mysql -e "REPAIR TABLE user;"
    mysql.exe -u root --skip-password mysql -e "FLUSH PRIVILEGES;"
    
    REM Stop safe mode MySQL
    taskkill /F /IM mysqld.exe 2>nul
    timeout /t 3 /nobreak >nul
    
    REM Start MySQL normally
    echo Starting MySQL normally...
    start "MySQL" mysqld.exe --defaults-file=my.ini --standalone --console
    
    timeout /t 10 /nobreak >nul
    
    netstat -an | findstr ":3306" >nul
    if %errorlevel% == 0 (
        echo ========================================
        echo SUCCESS: MySQL repaired and running!
        echo ========================================
    ) else (
        echo ========================================
        echo REPAIR FAILED: Try fresh installation
        echo ========================================
    )
) else (
    echo ========================================
    echo SAFE MODE FAILED: Database severely corrupted
    echo Recommendation: Fresh MySQL installation
    echo ========================================
)

echo.
echo Press any key to continue...
pause >nul
