@echo off
echo Starting MySQL for XAMPP...

REM Kill any existing MySQL processes
taskkill /F /IM mysqld.exe 2>nul

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start MySQL
echo Starting MySQL server...
cd /d "d:\xampp\mysql\bin"
start "MySQL" mysqld.exe --defaults-file="..\my.ini" --standalone --console

REM Wait for MySQL to start
timeout /t 5 /nobreak >nul

REM Check if MySQL is running
netstat -an | findstr ":3306" >nul
if %errorlevel% == 0 (
    echo MySQL started successfully on port 3306
) else (
    echo MySQL failed to start
)

pause
