<?php
// Theme test
define('WP_USE_THEMES', false);
require_once('wp-load.php');

echo "<h1>Theme Test</h1>";

// Get current theme
$current_theme = get_template();
echo "<p>Current Theme: " . $current_theme . "</p>";

// Try to switch to default theme temporarily
$default_themes = array('twentytwentyfour', 'twentytwentythree', 'twentytwentytwo', 'twentytwentyone');

foreach ($default_themes as $theme) {
    if (wp_get_theme($theme)->exists()) {
        echo "<p>Found default theme: " . $theme . "</p>";
        
        // Temporarily switch theme
        switch_theme($theme);
        echo "<p>Switched to: " . get_template() . "</p>";
        
        // Test if site loads with default theme
        echo "<p><a href='http://localhost/deshiflix/' target='_blank'>Test site with " . $theme . "</a></p>";
        
        // Switch back
        switch_theme($current_theme);
        echo "<p>Switched back to: " . get_template() . "</p>";
        break;
    }
}

// Check theme files
$theme_dir = get_template_directory();
echo "<p>Theme Directory: " . $theme_dir . "</p>";

$index_file = $theme_dir . '/index.php';
if (file_exists($index_file)) {
    echo "<p>index.php exists</p>";
} else {
    echo "<p><strong>index.php NOT found!</strong></p>";
}

$functions_file = $theme_dir . '/functions.php';
if (file_exists($functions_file)) {
    echo "<p>functions.php exists</p>";
    $size = filesize($functions_file);
    echo "<p>functions.php size: " . number_format($size) . " bytes</p>";
} else {
    echo "<p>functions.php NOT found</p>";
}
?>
