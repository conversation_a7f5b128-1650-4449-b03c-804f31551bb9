@echo off
echo ========================================
echo XAMPP MySQL Fix Script
echo ========================================

REM Stop any running MySQL processes
echo Stopping MySQL processes...
taskkill /F /IM mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

REM Backup current my.ini
echo Backing up current my.ini...
if exist "d:\xampp\mysql\bin\my.ini" (
    copy "d:\xampp\mysql\bin\my.ini" "d:\xampp\mysql\bin\my.ini.backup" >nul
    echo Current my.ini backed up
)

REM Copy fixed my.ini
echo Installing fixed my.ini...
copy "my-fixed.ini" "d:\xampp\mysql\bin\my.ini" >nul

REM Check MySQL data directory permissions
echo Checking MySQL data directory...
if not exist "d:\xampp\mysql\data" (
    echo Creating MySQL data directory...
    mkdir "d:\xampp\mysql\data"
)

REM Try to start MySQL
echo Starting MySQL...
cd /d "d:\xampp\mysql\bin"
start "MySQL" mysqld.exe --defaults-file="my.ini" --standalone --console

REM Wait for MySQL to start
echo Waiting for MySQL to start...
timeout /t 10 /nobreak >nul

REM Check if MySQL is running
netstat -an | findstr ":3306" >nul
if %errorlevel% == 0 (
    echo ========================================
    echo SUCCESS: MySQL is now running on port 3306
    echo ========================================
) else (
    echo ========================================
    echo ERROR: MySQL failed to start
    echo Check the console window for error messages
    echo ========================================
)

echo.
echo Press any key to continue...
pause >nul
