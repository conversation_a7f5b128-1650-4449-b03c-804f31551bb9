@echo off
echo ========================================
echo Fresh MySQL Installation Script
echo ========================================

REM Stop MySQL
echo Stopping MySQL...
taskkill /F /IM mysqld.exe 2>nul
timeout /t 3 /nobreak >nul

REM Backup your WordPress database
echo Backing up WordPress database...
if not exist "d:\xampp\mysql\data-backup" mkdir "d:\xampp\mysql\data-backup"
if exist "d:\xampp\mysql\data\wp" (
    xcopy "d:\xampp\mysql\data\wp" "d:\xampp\mysql\data-backup\wp" /E /I /Y >nul
    echo WordPress database backed up
)

REM Rename corrupted mysql system database
echo Renaming corrupted system database...
if exist "d:\xampp\mysql\data\mysql" (
    move "d:\xampp\mysql\data\mysql" "d:\xampp\mysql\data\mysql-corrupted" >nul
)

REM Initialize fresh MySQL system database
echo Initializing fresh MySQL system database...
cd /d "d:\xampp\mysql\bin"
mysqld.exe --initialize-insecure --basedir="d:\xampp\mysql" --datadir="d:\xampp\mysql\data"

REM Wait for initialization
timeout /t 5 /nobreak >nul

REM Start MySQL
echo Starting MySQL...
start "MySQL" mysqld.exe --defaults-file=my.ini --standalone --console

REM Wait for MySQL to start
timeout /t 10 /nobreak >nul

REM Check if MySQL is running
netstat -an | findstr ":3306" >nul
if %errorlevel% == 0 (
    echo ========================================
    echo SUCCESS: Fresh MySQL is running!
    echo ========================================
    echo.
    echo Next steps:
    echo 1. Open phpMyAdmin: http://localhost/phpmyadmin/
    echo 2. Create database 'wp'
    echo 3. Import your WordPress backup
    echo.
) else (
    echo ========================================
    echo FAILED: MySQL still not starting
    echo ========================================
    echo.
    echo Try:
    echo 1. Restart XAMPP completely
    echo 2. Check Windows Services
    echo 3. Reinstall XAMPP
    echo.
)

echo Press any key to continue...
pause >nul
