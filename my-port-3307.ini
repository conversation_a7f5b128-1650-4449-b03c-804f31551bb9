# MySQL Configuration for XAMPP - Port 3307
[mysql]
default-character-set=utf8mb4
port=3307

[mysqld]
port=3307
socket="D:/xampp/mysql/mysql.sock"
basedir="D:/xampp/mysql"
tmpdir="D:/xampp/tmp"
datadir="D:/xampp/mysql/data"
pid_file="mysql.pid"
key_buffer_size=16M
max_allowed_packet=1M
sort_buffer_size=512K
net_buffer_length=8K
read_buffer_size=256K
read_rnd_buffer_size=512K
myisam_sort_buffer_size=8M
log_error="mysql_error.log"

plugin_dir="D:/xampp/mysql/lib/plugin/"

server-id=1

innodb_data_home_dir="D:/xampp/mysql/data"
innodb_data_file_path=ibdata1:10M:autoextend
innodb_log_group_home_dir="D:/xampp/mysql/data"
innodb_buffer_pool_size=16M
innodb_log_file_size=5M
innodb_log_buffer_size=8M
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=50

[mysqldump]
quick
max_allowed_packet=16M

[mysql]
no-auto-rehash

[isamchk]
key_buffer_size=20M
sort_buffer_size=20M
read_buffer=2M
write_buffer=2M

[myisamchk]
key_buffer_size=20M
sort_buffer_size=20M
read_buffer=2M
write_buffer=2M

[mysqlhotcopy]
interactive-timeout
