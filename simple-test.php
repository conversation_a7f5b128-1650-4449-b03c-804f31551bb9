<?php
// Simple WordPress test
echo "Starting WordPress test...<br>";

// Check if WordPress files exist
if (file_exists('wp-config.php')) {
    echo "wp-config.php exists<br>";
} else {
    echo "wp-config.php NOT found<br>";
}

if (file_exists('wp-load.php')) {
    echo "wp-load.php exists<br>";
} else {
    echo "wp-load.php NOT found<br>";
}

// Try to load WordPress
echo "Attempting to load WordPress...<br>";
try {
    require_once('wp-load.php');
    echo "WordPress loaded successfully!<br>";
    echo "Site URL: " . get_site_url() . "<br>";
    echo "Home URL: " . get_home_url() . "<br>";
} catch (Exception $e) {
    echo "Error loading WordPress: " . $e->getMessage() . "<br>";
}
?>
