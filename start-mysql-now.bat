@echo off
echo ========================================
echo Starting MySQL for XAMPP
echo ========================================

REM Kill any existing MySQL processes
echo Stopping any running MySQL...
taskkill /F /IM mysqld.exe 2>nul
timeout /t 2 /nobreak >nul

REM Start MySQL
echo Starting MySQL server...
cd /d "d:\xampp\mysql\bin"

REM Start MySQL with console output
echo Starting mysqld.exe...
start "MySQL Server" mysqld.exe --defaults-file="my.ini" --standalone --console

REM Wait for MySQL to start
echo Waiting 10 seconds for MySQL to start...
timeout /t 10 /nobreak >nul

REM Check if MySQL is running
echo Checking if MySQL is running...
netstat -an | findstr ":3306" >nul
if %errorlevel% == 0 (
    echo.
    echo ========================================
    echo SUCCESS! MySQL is running on port 3306
    echo ========================================
    echo.
    echo You can now:
    echo 1. Open phpMyAdmin: http://localhost/phpmyadmin/
    echo 2. Access your site: http://localhost/deshiflix/
    echo.
) else (
    echo.
    echo ========================================
    echo ERROR: MySQL failed to start
    echo ========================================
    echo.
    echo Troubleshooting:
    echo 1. Check if port 3306 is free
    echo 2. Check MySQL error log
    echo 3. Try XAMPP Control Panel
    echo.
)

echo Press any key to exit...
pause >nul
