<!DOCTYPE html>
<html>
<head>
    <title>DeshiFlix - Emergency Mode</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f4f4f4; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 DeshiFlix - Emergency Mode</h1>
        
        <?php
        // Check if WordPress is working
        if (file_exists('wp-config.php')) {
            echo '<div class="status success">✅ WordPress files found</div>';
            
            try {
                define('WP_USE_THEMES', false);
                require_once('wp-load.php');
                echo '<div class="status success">✅ WordPress loaded successfully</div>';
                echo '<div class="status info">📊 Site URL: ' . get_site_url() . '</div>';
                echo '<div class="status info">🎨 Active Theme: ' . get_template() . '</div>';
                
                // Check database
                global $wpdb;
                $post_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish'");
                echo '<div class="status info">📝 Published Posts: ' . $post_count . '</div>';
                
            } catch (Exception $e) {
                echo '<div class="status error">❌ WordPress Error: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<div class="status error">❌ WordPress not found</div>';
        }
        ?>
        
        <h2>🔧 Quick Actions</h2>
        <a href="http://localhost/deshiflix/" class="btn">🏠 Try Main Site</a>
        <a href="http://localhost/deshiflix/wp-admin/" class="btn">⚙️ WordPress Admin</a>
        <a href="http://localhost/phpmyadmin/" class="btn">🗄️ phpMyAdmin</a>
        <a href="test.php" class="btn">🧪 PHP Test</a>
        
        <h2>📋 System Status</h2>
        <?php
        echo '<div class="status info">🐘 PHP Version: ' . phpversion() . '</div>';
        echo '<div class="status info">💾 Memory Limit: ' . ini_get('memory_limit') . '</div>';
        echo '<div class="status info">⏱️ Max Execution Time: ' . ini_get('max_execution_time') . 's</div>';
        echo '<div class="status info">📁 Document Root: ' . $_SERVER['DOCUMENT_ROOT'] . '</div>';
        ?>
        
        <h2>🚨 If Site Still Not Working</h2>
        <ol>
            <li>Restart XAMPP completely</li>
            <li>Check if port 80 is free</li>
            <li>Disable all plugins temporarily</li>
            <li>Switch to default WordPress theme</li>
            <li>Check error logs in XAMPP</li>
        </ol>
    </div>
</body>
</html>
